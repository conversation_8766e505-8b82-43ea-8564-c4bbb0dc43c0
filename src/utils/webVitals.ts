// Core Web Vitals monitoring for production
// Ultra-simplified version to avoid build issues

interface WebVitalMetric {
  name: string;
  value: number;
  rating: string;
}

interface WebVitalsConfig {
  reportToAnalytics?: boolean;
  reportToConsole?: boolean;
}

function reportMetric(metric: WebVitalMetric, config: WebVitalsConfig) {
  if (config.reportToConsole) {
    console.log(metric.name + ': ' + metric.value.toFixed(2) + 'ms (' + metric.rating + ')');
  }
}

export function initWebVitals(config: WebVitalsConfig = {}) {
  if (typeof window === 'undefined') {
    return;
  }

  const defaultConfig: WebVitalsConfig = {
    reportToAnalytics: true,
    reportToConsole: false,
    ...config
  };

  // Simple performance tracking
  setTimeout(function() {
    try {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigation) {
        const ttfb = navigation.responseStart - navigation.requestStart;
        const metric: WebVitalMetric = {
          name: 'TTFB',
          value: ttfb,
          rating: ttfb <= 800 ? 'good' : ttfb <= 1800 ? 'needs-improvement' : 'poor'
        };
        reportMetric(metric, defaultConfig);
      }
    } catch (e) {
      console.warn('Performance monitoring not supported');
    }
  }, 1000);
}

export function startWebVitalsMonitoring() {
  if (typeof window !== 'undefined') {
    if (document.readyState === 'complete') {
      initWebVitals();
    } else {
      window.addEventListener('load', function() {
        setTimeout(function() {
          initWebVitals();
        }, 1000);
      });
    }
  }
}

export default initWebVitals;
