import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react(),
    // Custom plugin to remove console logs in production
    {
      name: 'remove-console',
      transform(code, _id) {
        if (process.env.NODE_ENV === 'production') {
          return {
            code: code.replace(/console\.(log|debug|info|warn|error|assert|dir|dirxml|trace|group|groupEnd|time|timeEnd|profile|profileEnd|count)\(.*?\);?/g, ''),
            map: null
          }
        }
      }
    }
  ],
  build: {
    // Enable minification and optimization (using esbuild which is faster)
    minify: 'esbuild',
    // Enable code splitting for better lazy loading
    rollupOptions: {
      // Enhanced tree shaking
      treeshake: {
        moduleSideEffects: false,
        propertyReadSideEffects: false,
        tryCatchDeoptimization: false
      },
      output: {
        manualChunks: (id) => {
          // More granular chunking strategy
          if (id.includes('node_modules')) {
            // Core React libraries
            if (id.includes('react') || id.includes('react-dom')) {
              return 'react-vendor';
            }
            // Firebase - split into smaller chunks
            if (id.includes('firebase/app')) {
              return 'firebase-core';
            }
            if (id.includes('firebase/auth')) {
              return 'firebase-auth';
            }
            if (id.includes('firebase/firestore')) {
              return 'firebase-firestore';
            }
            if (id.includes('firebase')) {
              return 'firebase-other';
            }
            // UI libraries
            if (id.includes('lucide-react')) {
              return 'icons-vendor';
            }
            // Router
            if (id.includes('react-router')) {
              return 'router-vendor';
            }
            // Analytics
            if (id.includes('@google-analytics')) {
              return 'analytics-vendor';
            }
            // Group other vendor libraries
            return 'vendor';
          }

          // Split pages into separate chunks
          if (id.includes('src/pages/')) {
            const pageName = id.split('/pages/')[1].split('.')[0].toLowerCase();
            return `page-${pageName}`;
          }

          // Split components by feature
          if (id.includes('src/components/')) {
            // Admin components
            if (id.includes('PricingManagement') || id.includes('DiscountCodeManagement') || id.includes('RealTimeAnalytics')) {
              return 'admin-components';
            }
            // Form components
            if (id.includes('Form') || id.includes('Input') || id.includes('Select') || id.includes('Textarea')) {
              return 'form-components';
            }
            // Performance monitoring (dev only)
            if (id.includes('PerformanceMonitor')) {
              return 'dev-components';
            }
          }

          // Split utilities
          if (id.includes('src/utils/')) {
            if (id.includes('performance')) {
              return 'performance-utils';
            }
            if (id.includes('serviceWorker')) {
              return 'sw-utils';
            }
          }
        }
      }
    },
    // Improve chunk size warnings threshold
    chunkSizeWarningLimit: 500,
    // Enable source maps for production debugging (optional)
    sourcemap: false,
    // Optimize CSS
    cssMinify: true
  },
  // Optimize deps for faster dev server
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'lucide-react',
      'firebase/app',
      'firebase/auth',
      'firebase/firestore',
      'react-router-dom'
    ],
    exclude: ['firebase']
  },
  // Configure server for development
  server: {
    headers: {
      // Cache static assets for 1 year
      'Cache-Control': 'public, max-age=31536000, immutable'
    }
  },
  // Configure preview server
  preview: {
    headers: {
      'Cache-Control': 'public, max-age=31536000, immutable'
    }
  }
})
